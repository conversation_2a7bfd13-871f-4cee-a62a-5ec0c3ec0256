<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '用户信息',
    navigationStyle: 'custom',
    navigationBarTextStyle: 'white',
  },
}
</route>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { Color } from '@/enums/colorEnum'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'UserPage',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

const userStore = useUserStore()
const { userCode, inBlacklist } = storeToRefs(userStore)
const miniShopStatusStr = ref('')
const tempName = ref()
const iconSize = 28

onShow(() => {
  userStore.login().then(() => {
    userStore.getUserServerInfo().then((res) => {
      if (res.data.qrAuth && res.data.qrOrderInfo?.expiryDate) {
        miniShopStatusStr.value
          = `到期时间：${dayjs(res.data.qrOrderInfo.expiryDate).format('YYYY-MM-DD')}`
        tempName.value = res.data.qrOrderInfo.tempName
      }
      else {
        miniShopStatusStr.value = '未开通'
      }
    })
  })
})

function toOrderPage(tab: string) {
  uni.navigateTo({
    url: `/pages/myOrder/index?tab=${tab}`,
  })
}
</script>

<template>
  <view class="f-bg overflow-hidden p-4">
    <view :style="{ marginTop: `${safeAreaInsets?.top}px` }">
      <view class="flex items-center px-4 pb-4 pt-8 text-white space-x-4">
        <view class="f-user center flex-col rounded-full bg-white/60">
          <up-icon color="#fff" :size="36" name="account-fill" />
        </view>
        <view>
          <view class="text-sm text-white/80">
            用户编号
          </view>
          <view class="text-2xl font-bold">
            {{ userCode }}
          </view>
        </view>
      </view>
      <template v-if="!inBlacklist">
        <view
          class="mb-3 mt-3 flex items-center justify-between rounded bg-white px-6 py-4 text-sm"
        >
          <view class="center flex-col" @click="toOrderPage('all')">
            <up-icon :size="iconSize" :color="Color.primary" name="order" />
            <view class="mt-1">
              全部订单
            </view>
          </view>
          <view class="center flex-col" @click="toOrderPage('waitSend')">
            <up-icon :size="iconSize" :color="Color.primary" name="bag" />
            <view class="mt-1">
              待发货
            </view>
          </view>
          <view class="center flex-col" @click="toOrderPage('waitReceive')">
            <up-icon :size="iconSize" :color="Color.primary" name="car" />
            <view class="mt-1">
              待收货
            </view>
          </view>
          <!-- <view class="center flex-col" @click="toOrderPage('waitEvaluate')">
            <up-icon :size="iconSize" name="more-circle"></up-icon>
            <view>待评价</view>
          </view> -->
          <view class="center flex-col" @click="toOrderPage('refund')">
            <up-icon :size="iconSize" :color="Color.primary" name="rmb-circle" />
            <view class="mt-1">
              退款售后
            </view>
          </view>
        </view>
        <up-cell-group custom-class="bg-white py-2 rounded" :border="false">
          <!-- <up-cell title="物流订单" is-link url="/pages/myOrder/logisticsOrder" /> -->
          <!-- <up-cell title="订单开票" is-link url="/pages/myOrder/orderInvoicing" /> -->
          <up-cell title="收货地址" is-link url="/pages/addressPage/index" />
          <up-cell title="开票信息" is-link url="/pages/invoiceTemple/index" />
          <up-cell title="红包卡券" is-link url="/pages/discountCoupon/index" :border="false" />
        </up-cell-group>

        <up-cell-group custom-class="bg-white py-2 rounded mt-3" :border="false">
          <up-cell title="关于" is-link url="/pages/about/index" :border="false" />
        </up-cell-group>
        <!-- <cs-long-button /> -->
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-user {
  $w: 3rem;
  width: $w;
  height: $w;
}
.f-bg {
  background: linear-gradient(90deg, #fa4c03 0%, #ff5b00 48.56%, #fd7424 100%);
  background-repeat: no-repeat;
  background-size: 100% 56vw;
}
</style>
